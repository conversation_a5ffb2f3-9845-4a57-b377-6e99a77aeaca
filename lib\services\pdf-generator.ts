import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'
import { ApiClient } from '@/lib/api-client'
import type { RecordsResponse, RecordTable } from '@/lib/api-client-types'
import { mapTableIdWithName } from '../queries/records'
// Helper function to format currency in French format
function formatCurrencyFrench(amount: number): string {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'MAD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Helper function to format date in French format
function formatDateFrench(dateString: string): string {
  if (!dateString || dateString === 'Invalid Date') return '-'
  
  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) return '-'
    
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    return '-'
  }
}

// Simple PDF download utility - FOCUSED ON OPENING IN PDF READER
class PDFDownloadUtility {
  /**
   * Force PDF download with proper MIME type to ensure it opens in PDF reader
   */
  static async downloadPDF(blob: Blob, fileName: string): Promise<void> {
    // Create blob with explicit PDF MIME type
    const pdfBlob = new Blob([blob], { 
      type: 'application/pdf'
    })
    
    // Create object URL
    const url = URL.createObjectURL(pdfBlob)
    
    // Create download link with proper attributes to force download
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    
    // CRITICAL: These attributes ensure PDF opens in PDF reader, not browser
    link.setAttribute('download', fileName)
    link.setAttribute('type', 'application/pdf')
    link.setAttribute('target', '_self')
    
    // Hide link
    link.style.display = 'none'
    link.style.position = 'absolute'
    link.style.left = '-9999px'
    
    // Add to DOM, trigger download, then cleanup
    document.body.appendChild(link)
    link.click()
    
    // Cleanup after download starts
    setTimeout(() => {
      if (document.body.contains(link)) {
        document.body.removeChild(link)
      }
      URL.revokeObjectURL(url)
    }, 1000)
  }
}

// Helper function to create HTML content for PDF
function createPDFHTML(
  tableName: string,
  tableColor: string,
  coffreTotal: number,
  lastModificationDate: string,
  records: RecordTable[]
): string {
  return `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap');

        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: 'Noto Sans Arabic', Arial, sans-serif;
          direction: rtl;
          background: white;
          width: 210mm;
          min-height: 297mm;
          padding: 0;
          margin: 0;
          position: relative;
          overflow: hidden;
        }

        .pdf-container {
          width: 100%;
          background: white;
        }

        .header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px;
          border-bottom: 3px solid black;
          margin-bottom: 20px;
        }

        .header-amount {
          font-size: 24px;
          font-weight: bold;
          color: black;
        }

        .header-date {
          font-size: 18px;
          color: black;
          font-weight: 500;
        }

        .table-name {
          text-align: center;
          font-size: 28px;
          font-weight: bold;
          color: white;
          background-color: ${tableColor};
          padding: 15px;
          margin: 0 20px 30px 20px;
          border-radius: 8px;
        }

        .table-container {
          width: calc(100% - 40px);
          margin: 0 20px;
          border-collapse: collapse;
          font-size: 14px;
        }

        .table-header th {
          padding: 12px 8px;
          text-align: center;
          font-weight: bold;
          font-size: 14px;
          color: black;
          border: 2px solid black;
        }

        .table-row {
          border: 1px solid #ddd;
        }

        .table-row:nth-child(even) {
          background-color: #f8f8f8;
        }

        .table-row td {
          padding: 10px 8px;
          text-align: center;
          font-size: 12px;
          color: #333;
          border: 2px solid black;
          vertical-align: middle;
        }

        .amount {
          font-weight: bold;
        }

        .name-cell {
          font-weight: 500;
        }

        .comment-cell {
          font-style: italic;
          color: #666;
          font-size: 11px;
        }
      </style>
    </head>
    <body>
      <div class="pdf-container">
        <!-- Header Section -->
        <div class="header">
          <div class="header-amount">${formatCurrencyFrench(coffreTotal)}</div>
          <div class="header-date">${formatDateFrench(lastModificationDate)}</div>
        </div>

        <!-- Table Name -->
        <div class="table-name">${tableName}</div>

        <!-- Table -->
        <table class="table-container">
          <thead class="table-header">
            <tr>
              <th>الاسم</th>
              <th>المبلغ</th>
              <th>تاريخ الخروج</th>
              <th>تاريخ التحصيل</th>
              <th>ملاحظات</th>
            </tr>
          </thead>
          <tbody>
            ${records.map(record => `
              <tr class="table-row">
                <td class="name-cell">${record.nomComplet}</td>
                <td class="amount">${formatCurrencyFrench(record.montant)}</td>
                <td>${formatDateFrench(record.dateSortie)}</td>
                <td>${formatDateFrench(record.dateRetour)}</td>
                <td class="comment-cell">${record.commentaire || "لا يوجد تعليق"}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    </body>
    </html>
  `
}

export interface PDFGenerationOptions {
  tableId: string | number
  tableName: string
  tableColor: string
  coffreTotal: number
  lastModificationDate: string
}

export class PDFGeneratorService {
  /**
   * Fetch all records for a table (using MAX_INT equivalent)
   */
  private async fetchAllRecords(tableId: string | number): Promise<RecordsResponse> {
    try {
      // Use a very large number to fetch all records
      const response = await ApiClient.getRecords(mapTableIdWithName(tableId), {
        page: 0,
        size: 2147483647 // MAX_INT equivalent
      })
      return response
    } catch (error) {
      console.error('Failed to fetch all records for PDF:', error)
      throw new Error('Failed to fetch records for PDF generation')
    }
  }

  /**
   * Create a temporary DOM element with the PDF content in an isolated container
   */
  private createTempElement(htmlContent: string): HTMLElement {
    // Create an isolated iframe to prevent layout interference
    const iframe = document.createElement('iframe')
    iframe.style.position = 'absolute'
    iframe.style.left = '-99999px'
    iframe.style.top = '-99999px'
    iframe.style.width = '210mm'
    iframe.style.height = '297mm'
    iframe.style.border = 'none'
    iframe.style.visibility = 'hidden'
    iframe.style.opacity = '0'
    iframe.style.zIndex = '-9999'

    document.body.appendChild(iframe)

    // Write content to iframe
    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document
    if (!iframeDoc) {
      throw new Error('Could not access iframe document')
    }

    iframeDoc.open()
    iframeDoc.write(htmlContent)
    iframeDoc.close()

    // Return the body element of the iframe
    return iframeDoc.body
  }

  /**
   * Generate PDF for a specific table using HTML-to-canvas approach
   */
  async generateTablePDF(options: PDFGenerationOptions): Promise<Blob> {
    let tempElement: HTMLElement | null = null
    let iframe: HTMLIFrameElement | null = null

    try {
      // Fetch all records
      const recordsResponse = await this.fetchAllRecords(options.tableId)

      // Create HTML content
      const htmlContent = createPDFHTML(
        options.tableName,
        options.tableColor,
        options.coffreTotal,
        options.lastModificationDate,
        recordsResponse.elements
      )

      // Create temporary DOM element in isolated iframe
      tempElement = this.createTempElement(htmlContent)

      // Find the iframe that contains our element
      iframe = Array.from(document.querySelectorAll('iframe')).find(frame => {
        try {
          return frame.contentDocument?.body === tempElement
        } catch {
          return false
        }
      }) || null

      // Wait for fonts to load and give extra time for Arabic fonts
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Generate canvas from HTML with better options
      const canvas = await html2canvas(tempElement, {
        scale: 2, // Higher quality
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: tempElement.scrollWidth,
        height: tempElement.scrollHeight,
        logging: false,
        removeContainer: false,
        foreignObjectRendering: true
      })

      // Create PDF
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      })

      const imgData = canvas.toDataURL('image/png', 1.0)
      const imgWidth = 210 // A4 width in mm
      const pageHeight = 297 // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width
      let heightLeft = imgHeight

      let position = 0

      // Add first page
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
      heightLeft -= pageHeight

      // Add additional pages if needed
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight
        pdf.addPage()
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
        heightLeft -= pageHeight
      }

      return pdf.output('blob')

    } catch (error) {
      console.error('PDF generation failed:', error)
      throw new Error('Failed to generate PDF')
    } finally {
      // Always clean up the iframe
      if (iframe && document.body.contains(iframe)) {
        try {
          document.body.removeChild(iframe)
        } catch (cleanupError) {
          console.warn('Failed to cleanup iframe:', cleanupError)
        }
      }
    }
  }

  /**
   * Download PDF file - FORCE DOWNLOAD TO OPEN IN PDF READER
   */
  async downloadTablePDF(options: PDFGenerationOptions): Promise<void> {
    try {
      const blob = await this.generateTablePDF(options)
      const fileName = `${options.tableName}_${formatDateFrench(options.lastModificationDate)}.pdf`

      // Log download attempt for debugging
      console.log('🔥 PDF Download initiated:', {
        fileName,
        blobSize: blob.size,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      })

      // Use simple download utility focused on PDF reader opening
      await PDFDownloadUtility.downloadPDF(blob, fileName)

      // Log successful download initiation
      console.log('✅ PDF Download method completed successfully')

    } catch (error) {
      console.error('❌ PDF download failed:', error)
      throw error
    }
  }
}

// Export singleton instance
export const pdfGenerator = new PDFGeneratorService()
