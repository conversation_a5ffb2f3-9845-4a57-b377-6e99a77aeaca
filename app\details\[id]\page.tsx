"use client"

import { <PERSON><PERSON><PERSON><PERSON>, ArrowRight, FileText, Eye, EyeOff, Edit, Trash2, Plus, Search, X, Clock, Refresh<PERSON><PERSON>, ChevronUp, Download } from "lucide-react"
import { useState, useEffect, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

import { EditRecordDialog } from "@/components/edit-record-dialog"
import { useLanguage } from "@/contexts/language-context"
import { useDirection } from "@radix-ui/react-direction"
import { usePDFGenerator } from "@/lib/hooks/use-pdf-generator"
import Link from "next/link"
import {
  useCreateRecord,
  useUpdateRecord,
  useDeleteRecord
} from "@/lib/queries/records"
import { useFreshTableRecords } from "@/hooks/use-fresh-table-records"
import { useUpdateTable } from "@/lib/queries/tables"
import { useSearchParams } from "next/navigation"
import { Table, RecordTable } from "@/lib/api-client-types"

interface PageProps {
  params: { id: string }
  searchParams?: { [key: string]: string | string[] | undefined }
}

export default function DetailsPage({ params }: PageProps) {
  // Extract search parameters
  const searchParams = useSearchParams()

  const [isPriceHidden, setIsPriceHidden] = useState(false)
  const [hiddenContent, setHiddenContent] = useState<Set<string>>(new Set())
  const [editingRecord, setEditingRecord] = useState<any>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)

  const tableIdentifier = params.id

  const {
    data: recordsResponse,
    isLoading: isLoadingRecords,
    error: recordsError,
    refetch: refetchRecords,
  } = useFreshTableRecords(tableIdentifier, { page: 0, size: 2147483647 }) // Fetch all records with MAX INT limit

  // Extract all records from API response (no pagination)
  const totalElements = recordsResponse?.totalElements || 0

  // Store the table ID for CRUD operations
  const tableId = params.id

  // React Query Mutation Hooks
  const createRecordMutation = useCreateRecord(tableId)
  const updateRecordMutation = useUpdateRecord(tableId)
  const deleteRecordMutation = useDeleteRecord(tableId)


  const [isTableEditOpen, setIsTableEditOpen] = useState(false)
  const [editTableName, setEditTableName] = useState("")
  const [editTableColor, setEditTableColor] = useState("")
  const [editTableInitialAmount, setEditTableInitialAmount] = useState("")
  const [displayInitialAmount, setDisplayInitialAmount] = useState("")

  // Helper function to format number with thousands separator
  const formatNumberDisplay = (value: number | string): string => {
    if (value === "" || value === null || value === undefined) return ""
    const numValue = typeof value === 'string' ? parseFloat(value) : value
    if (isNaN(numValue)) return ""

    // Convert to string and add dots every 3 digits from right to left
    const numStr = Math.floor(numValue).toString()
    return numStr.replace(/\B(?=(\d{3})+(?!\d))/g, '.')
  }

  // Helper function to parse formatted number back to integer
  const parseFormattedNumber = (formattedValue: string): number => {
    if (!formattedValue) return 0
    // Remove all dots (thousands separators) and parse as integer
    const cleanValue = formattedValue.replace(/\./g, '')
    return parseInt(cleanValue) || 0
  }
  const [currentDisplayColor, setCurrentDisplayColor] = useState("")
  const [isTableNameHidden, setIsTableNameHidden] = useState(false)
  const [recordToDelete, setRecordToDelete] = useState<RecordTable | null>(null)
  const [searchByName, setSearchByName] = useState("")
  const [searchByMontant, setSearchByMontant] = useState("")
  const [selectedRecord, setSelectedRecord] = useState<any>(null)
  const [isActionMenuOpen, setIsActionMenuOpen] = useState(false)

  // Sticky summary state
  const [showStickySummary, setShowStickySummary] = useState(false)

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  // PDF generation function with enhanced mobile support
  const handlePDFDownload = async () => {
    if (!currentTableData || !recordsResponse) return

    try {
      const deviceInfo = {
        userAgent: navigator.userAgent,
        isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
        isIOS: /iPad|iPhone|iPod/.test(navigator.userAgent),
        isAndroid: /Android/i.test(navigator.userAgent)
      }

      console.log('🔥 PDF Download initiated from details page:', {
        tableId: params.id,
        tableName: currentTableName,
        recordCount: transformedRecords.length,
        deviceInfo
      })

      // Use the enhanced PDF generation with better mobile support
      await generatePDF({
        tableId: params.id,
        tableName: currentTableName || 'Table',
        tableColor: currentTableColor || '#8BC34A',
        coffreTotal: recordsResponse.coffreTotal || 0,
        lastModificationDate: recordsResponse.lastDateModification || new Date().toISOString()
      })

      console.log('✅ PDF Download completed successfully')

    } catch (error) {
      console.error('❌ PDF generation failed:', error)
    }
  }

  const { t, isRTL, formatDate, formatCurrencyFrench } = useLanguage()
  const dir = useDirection()
  const { generatePDF, isGenerating } = usePDFGenerator()
  const tableDataParam = searchParams.get('tableData')
  let initialTableObject = null

  try {
    if (tableDataParam) {
      initialTableObject = JSON.parse(decodeURIComponent(tableDataParam))
    }
  } catch (error) {
    console.error('❌ Error parsing table data from URL:', error)
  }

  // State to hold current table data (will be updated from API response)
  const [currentTableData, setCurrentTableData] = useState<Table>(initialTableObject)

  const currentTableName = currentTableData?.coffreNameIhm
  const currentTableColor = currentTableData?.coffreColor

  // Initialize display color on first load
  useEffect(() => {
    if (!currentDisplayColor) {
      setCurrentDisplayColor(convertTailwindToHex(currentTableColor))
    }
  }, [currentTableColor, currentDisplayColor])

  // CRUD Operations using React Query mutations
  const handleDeleteRecord = async (recordId: string) => {
    try {
      await deleteRecordMutation.mutateAsync(parseInt(recordId))
       // Small delay to ensure API operation completes
      await new Promise(resolve => setTimeout(resolve, 100))
      // Force refetch to ensure UI is updated
      await refetchRecords()
    } catch (error) {
      // Error toast handled by the mutation hook
      console.error("Error deleting record:", error)
    }
  }

  const handleUpdateRecord = async (recordId: string, updatedData: any) => {
    try {
      await updateRecordMutation.mutateAsync({
        id: parseInt(recordId),
        data: updatedData
      })
      // Small delay to ensure API operation completes
      await new Promise(resolve => setTimeout(resolve, 100))
      // Force refetch to ensure UI is updated
      await refetchRecords()

    } catch (error) {
      // Error toast handled by the mutation hook
      console.error("Error updating record:", error)
    }
  }

  const handleCreateRecord = async (newRecordData: any) => {
    try {
      await createRecordMutation.mutateAsync(newRecordData)
      // Small delay to ensure API operation completes
      await new Promise(resolve => setTimeout(resolve, 100))
      // Force refetch to ensure UI is updated
      await refetchRecords()
      console.log("✅ Record created and data refreshed")
    } catch (error) {
      // Error toast handled by the mutation hook
      console.error("Error creating record:", error)
    }
  }

  // Clear search filters (local filtering only)
  const clearSearch = () => {
    setSearchByName("")
    setSearchByMontant("")
  }

  // Helper function to convert Tailwind classes to hex codes
  const convertTailwindToHex = (color: string): string => {
    const tailwindToHex: Record<string, string> = {
      "bg-blue-500": "#3B82F6",
      "bg-purple-500": "#A855F7",
      "bg-green-500": "#10B981",
      "bg-yellow-500": "#F59E0B",
      "bg-red-500": "#EF4444",
      "bg-pink-500": "#EC4899",
      "bg-orange-500": "#F97316",
      "bg-gray-500": "#6B7280",
    }

    // If it's already a hex code, return as is
    if (color?.startsWith('#')) {
      return color
    }

    // Convert Tailwind class to hex, or return original if not found
    return tailwindToHex[color] || color
  }

  // Initialize edit form when dialog opens
  useEffect(() => {
    if (isTableEditOpen) {
      setEditTableName(currentTableName)
      // Convert Tailwind class to hex code if needed
      setEditTableColor(convertTailwindToHex(currentTableColor))
      // Initialize with current initial amount
      const initialAmount = currentTableData?.coffreTotal || 0
      setEditTableInitialAmount(initialAmount.toString())
      setDisplayInitialAmount(formatNumberDisplay(initialAmount))
    }
  }, [isTableEditOpen, currentTableName, currentTableColor, currentTableData?.coffreTotal])

  // Table update mutation
  const updateTableMutation = useUpdateTable()

  // Save table changes
  const handleSaveTableChanges = async () => {
    try {
      // Create updated table object with all original data plus changes
      const updatedTableData = {
        ...currentTableData,
        coffreName: editTableName,
        coffreNameIhm: editTableName,
        coffreColor: convertTailwindToHex(editTableColor), // Ensure hex code is stored
        coffreTotal: parseFormattedNumber(displayInitialAmount), // Include updated initial amount as integer
        dateDerniereModification: new Date().toISOString().split('T')[0]
      }

      // Call the API and get the response
      const apiResponse = await updateTableMutation.mutateAsync({
        id: parseInt(tableId),
        data: updatedTableData // Send the whole updated object
      })

      setCurrentTableData(apiResponse)

      // Update the display color state as well
      setCurrentDisplayColor(convertTailwindToHex(apiResponse.coffreColor || editTableColor))

      // Update the display color immediately in the UI
      setCurrentDisplayColor(convertTailwindToHex(editTableColor))

      // Close the dialog
      setIsTableEditOpen(false)

      // Manually refetch records to update financial data immediately
      await refetchRecords()
      console.log("🔄 Records data refetched after table update")

      // Success message will be shown by the mutation's onSuccess
    } catch (error) {
      console.error("❌ Failed to update table:", error)
      // Error message will be shown by the mutation's onError
    }
  }

  // Scroll event listener for sticky summary
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY
      const triggerPoint = 400 // Show sticky summary after scrolling 400px
      setShowStickySummary(scrollPosition > triggerPoint)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Use API data instead of static data
  const apiRecords = recordsResponse?.elements || []

  // Transform API records to match the expected format
  const transformedRecords = useMemo(() => {
    return apiRecords.map((record) => ({
      id: record.id.toString(),
      nameKey: record.nomComplet,
      value: record.montant,
      date: record.dateSortie,
      dateReturn: record.dateRetour,
      commentaire: record.commentaire,
    }))
  }, [apiRecords])

  // Filter records based on search inputs (real-time local filtering)
  const filteredRecords = useMemo(() => {
    let filtered = transformedRecords

    // Filter by name if searchByName has value
    if (searchByName.trim()) {
      const nameQuery = searchByName.toLowerCase().trim()
      filtered = filtered.filter((record) => {
        const recordName = record.nameKey.startsWith("record.") ? t(record.nameKey) : record.nameKey
        return recordName.toLowerCase().includes(nameQuery)
      })
    }

    // Filter by amount if searchByMontant has value
    if (searchByMontant.trim()) {
      const amountQuery = parseFloat(searchByMontant)
      if (!isNaN(amountQuery)) {
        filtered = filtered.filter((record) => {
          return record.value === amountQuery || record.value.toString().includes(searchByMontant)
        })
      }
    }

    return filtered
  }, [transformedRecords, searchByName, searchByMontant, t])

  // Determine if we're filtering/searching
  const isFiltering = searchByName.trim() || searchByMontant.trim()

  // Display all records (no pagination)
  const recordsToDisplay = filteredRecords

  const recordsCount = recordsResponse?.elements.length

  const handleEditRecord = (record: any) => {
    setEditingRecord(record)
    setIsEditDialogOpen(true)
  }

  const toggleContentVisibility = (recordId: string) => {
    const newHiddenContent = new Set(hiddenContent)
    if (newHiddenContent.has(recordId)) {
      newHiddenContent.delete(recordId)
    } else {
      newHiddenContent.add(recordId)
    }
    setHiddenContent(newHiddenContent)
  }

  const handleRecordClick = (record: any) => {
    setSelectedRecord(record)
    setIsActionMenuOpen(true)
  }

  const handleActionMenuEdit = () => {
    setIsActionMenuOpen(false)
    handleEditRecord(selectedRecord)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100" dir={dir}>
      {/* Header */}
      <header className="sticky top-0 z-50 bg-white border-b border-gray-200 px-4 py-3">
        <div className={`max-w-7xl mx-auto flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""} space-x-4`}>
            <Link href="/">
              <Button variant="ghost" size="icon">
                {isRTL ? (
                  <ArrowRight className="h-6 w-6 text-gray-600" />
                ) : (
                  <ArrowLeft className="h-6 w-6 text-gray-600" />
                )}
              </Button>
            </Link>
            <div className="w-full text-right" dir="rtl">
              <div className="flex items-center justify-end space-x-2">
                <div className="text-right">
                  <h1 className="text-xl font-semibold text-black text-right" dir="rtl">
                    {isTableNameHidden ? "••••••••" : (currentTableName)}
                  </h1>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 py-8">

        {/* Enhanced Sticky Horizontal Summary */}
        {showStickySummary && (
          <div className="fixed top-0 left-0 right-0 z-50 bg-white border-b-2 border-gray-300 shadow-lg">
            <div className="max-w-7xl mx-auto px-4 py-4">
              {/* Mobile Layout */}
              <div className="block md:hidden">
                {/* Title and Last Update Row */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex-1 mr-4">
                    <h3 className="font-bold text-gray-900 text-base truncate text-right" dir="rtl">
                      {currentTableName}
                    </h3>
                  </div>

                  <div className="flex items-center space-x-2">
                    {/* Combined Toggle Eye Icon - Mobile */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setIsPriceHidden(!isPriceHidden)
                        setIsTableNameHidden(!isTableNameHidden)
                      }}
                      className="text-gray-600 p-1 h-8 w-8"
                      title={isPriceHidden ? "Show All" : "Hide All"}
                    >
                      {isPriceHidden ? <EyeOff className="h-4 w-4 text-black" /> : <Eye className="h-4 w-4 text-black" />}
                    </Button>

                    {/* Compact Last Update - Mobile with RTL */}
                    <div className="bg-red-600 rounded-lg px-3 py-2 border border-red-500 shadow-lg">
                      <div className="flex items-center justify-center space-x-2 space-x-reverse" dir="rtl">
                        <span className="font-bold text-white text-sm date-french bg-black/20 rounded px-2 py-0.5">{formatDate(recordsResponse?.lastDateModification || "")}</span>
                        <span className="text-white/90 font-medium text-xs">آخر تحديث</span>
                        <Clock className="h-3 w-3 text-red-200 animate-pulse" />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Financial Data Grid */}
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div
                    className="rounded-lg p-2 text-center border border-gray-200"
                    style={{ backgroundColor: convertTailwindToHex(currentTableData?.coffreColor || currentTableColor) }}
                  >
                    <div className="text-black font-medium text-xs mb-1" dir="rtl">الأولي</div>
                    <div className="font-bold text-black number-french">
                      {isPriceHidden ? "••••" : formatCurrencyFrench(recordsResponse?.coffreTotal || 0)}
                    </div>
                  </div>

                  <div className="rounded-lg p-2 text-center bg-red-500 shadow-lg">
                    <div className="text-white font-medium text-xs mb-1" dir="rtl">الحالي</div>
                    <div className="font-bold text-white number-french">
                      {isPriceHidden ? "••••" : formatCurrencyFrench(recordsResponse?.rest || 0)}
                    </div>
                  </div>

                  <div
                    className="rounded-lg p-2 text-center border border-gray-200"
                    style={{ backgroundColor: convertTailwindToHex(currentTableData?.coffreColor || currentTableColor) }}
                  >
                    <div className="text-black font-medium text-xs mb-1" dir="rtl">المستهلك</div>
                    <div className="font-bold text-black number-french">
                      {isPriceHidden ? "••••" : formatCurrencyFrench(recordsResponse?.montantConsommee || 0)}
                    </div>
                  </div>

                  <div
                    className="rounded-lg p-2 text-center border border-gray-200"
                    style={{ backgroundColor: convertTailwindToHex(currentTableData?.coffreColor || currentTableColor) }}
                  >
                    <div className="text-black font-medium text-xs mb-1" dir="rtl">أشخاص</div>
                    <div className="font-bold text-black number-french">{recordsResponse?.totalElements}</div>
                  </div>
                </div>
              </div>

              {/* Desktop Layout */}
              <div className="hidden md:flex items-center justify-between overflow-x-auto">
                <div className="flex items-center space-x-2 space-x-reverse min-w-0 w-1/3 mr-6" dir="rtl">
                  <h3
                    className="font-semibold text-gray-900 truncate flex-1 cursor-pointer select-none hover:scale-105 transition-transform duration-200"
                    style={{direction: 'rtl', textAlign: 'right', unicodeBidi: 'embed'}}
                    onClick={() => setIsTableNameHidden(!isTableNameHidden)}
                  >
                    {isTableNameHidden ? "********" : (currentTableName.startsWith("home.") ? t(currentTableName) : currentTableName)}
                  </h3>
                </div>

                <div className="flex items-center space-x-4 text-sm">
                  <div className="flex items-center space-x-1">
                    <span className="text-gray-600">الأولي:</span>
                    <span className="font-semibold text-black number-french">
                      {isPriceHidden ? "••••" : formatCurrencyFrench(recordsResponse?.coffreTotal || 0)}
                    </span>
                  </div>

                  <div className="flex items-center space-x-1">
                    <span className="text-gray-600">الحالي:</span>
                    <span className="font-semibold text-black number-french">
                      {isPriceHidden ? "••••" : formatCurrencyFrench(recordsResponse?.rest || 0)}
                    </span>
                  </div>

                  <div className="flex items-center space-x-1">
                    <span className="text-gray-600">المستهلك:</span>
                    <span className="font-semibold text-black number-french">
                      {isPriceHidden ? "••••" : formatCurrencyFrench(recordsResponse?.montantConsommee || 0)}
                    </span>
                  </div>

                  <div className="flex items-center space-x-1">
                    <span className="text-gray-600">أشخاص:</span>
                    <span className="font-semibold text-black number-french">{totalElements}</span>
                  </div>

                  {/* Remarkable Last Update - Desktop */}
                  <div className="flex items-center space-x-2 bg-red-100 rounded-lg px-3 py-1 border border-red-300">
                    <div className="flex items-center space-x-1">
                      <Clock className="h-3 w-3 text-red-600 animate-pulse" />
                      <span className="text-xs font-semibold text-red-700">آخر تحديث:</span>
                    </div>
                    <span className="font-bold text-red-800 text-xs date-french">{formatDate(recordsResponse?.lastDateModification || "")}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        {/* Streamlined Summary Card */}
        <Card className={`mb-8 overflow-hidden border-0 shadow-lg ${showStickySummary ? 'mt-24 md:mt-16' : ''}`}>
          <CardHeader
            className="text-white p-6"
            style={{ backgroundColor: convertTailwindToHex(currentTableData?.coffreColor || currentTableColor) }}
          >
            {/* Header Section with Title and Controls */}
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center space-x-3 space-x-reverse" dir="rtl">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsTableEditOpen(true)}
                  className="p-2 text-white bg-white/20 hover:bg-white/30 border border-white/30 transition-all duration-300 rounded-lg"
                  title="تعديل الجدول"
                >
                  <Edit className="h-5 w-5 text-black" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setIsPriceHidden(!isPriceHidden)
                    setIsTableNameHidden(!isTableNameHidden)
                  }}
                  className="p-2 text-black bg-white/20 hover:bg-white/30 border border-white/30 transition-all duration-300 rounded-lg"
                  title={isPriceHidden ? "إظهار البيانات" : "إخفاء البيانات"}
                >
                  {isPriceHidden ? <EyeOff className="h-5 w-5 text-black" /> : <Eye className="h-5 w-5 text-black" />}
                </Button>
              </div>

              <div className="text-right" dir="rtl">
                <h1
                  className="text-3xl text-black cursor-pointer select-none"
                  onClick={() => {
                    setIsTableNameHidden(!isTableNameHidden)
                    setIsPriceHidden(!isPriceHidden)
                  }}
                >
                  {isTableNameHidden ? "••••••••" : (currentTableName)}
                </h1>
              </div>
            </div>

            {/* Financial Overview Cards */}
            <div className="flex justify-center mb-6">
              <div className="grid grid-cols-2 gap-4 max-w-md w-full">
                {/* Initial Amount Card */}
                <div className="text-center rounded-lg p-4 border border-white/20" style={{ backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
                  <div className="text-sm text-black opacity-80 mb-1 text-center">الأولي</div>
                  <div className="text-2xl font-bold text-black number-french transition-all duration-500 ease-in-out">
                    {isPriceHidden ? "••••••" : formatCurrencyFrench(recordsResponse?.coffreTotal || 0)}
                  </div>
                </div>

                {/* Current Amount Card */}
                <div className="text-center rounded-lg p-4 bg-red-500 shadow-lg">
                  <div className="text-sm text-white font-medium mb-1 text-center">الحالي</div>
                  <div className="text-2xl font-bold text-white number-french transition-all duration-500 ease-in-out">
                    {isPriceHidden ? "••••••" : formatCurrencyFrench(recordsResponse?.rest || 0)}
                  </div>
                </div>
              </div>
            </div>

            {/* Secondary Stats */}
            <div className="flex justify-center mb-6">
              <div className="grid grid-cols-2 gap-4 max-w-md w-full">
                {/* Consumed Amount */}
                <div className="text-center rounded-lg p-3 border border-white/20" style={{ backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
                  <div className="text-sm text-black opacity-80 mb-1 text-center">المستهلك</div>
                  <div className="text-2xl font-bold text-black number-french transition-all duration-500 ease-in-out">
                    {isPriceHidden ? "••••••" : formatCurrencyFrench(recordsResponse?.montantConsommee || 0)}
                  </div>
                </div>

                {/* Records Count */}
                <div className="text-center rounded-lg p-3 border border-white/20" style={{ backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
                  <div className="text-sm text-black opacity-80 mb-1 text-center">أشخاص</div>
                  <div className="text-2xl font-bold text-black number-french">{recordsResponse?.totalElements || 0}</div>
                </div>
              </div>
            </div>

            {/* Compact Last Update with RTL */}
            <div className="mt-4">
              <div className="bg-red-600 rounded-lg px-3 py-2 border border-red-500 shadow-lg">
                <div className="flex items-center justify-center space-x-2 space-x-reverse" dir="rtl">
                  <span className="font-bold text-white text-sm date-french bg-black/20 rounded px-2 py-0.5">{formatDate(recordsResponse?.lastDateModification || "")}</span>
                  <span className="font-medium text-xs">آخر تحديث</span>
                  <Clock className="h-3 w-3 text-red-200 animate-pulse" />
                </div>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Records List with Search and Multi-Select */}
        <Card className="shadow-lg">
          <CardHeader className="bg-white border-b border-gray-100">
            <div className={`flex items-center justify-between flex-row-reverse mb-4`}>
              <CardTitle
                className={`text-xl font-semibold text-gray-900 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                dir="rtl"
              >
                <FileText className={`h-5 w-5 text-[#0A66C2] ${isRTL ? "ml-2" : "mr-2"}`} />
                أشخاص ({recordsCount})
              </CardTitle>
              <div className={`flex items-center ${isRTL ? "flex-row-reverse space-x-reverse" : ""} space-x-2`}>
                <Button
                  onClick={() => {
                    console.log("➕ Opening add record dialog")
                    // Create empty record template for add mode
                    const emptyRecord = {
                      id: "new",
                      nameKey: "",
                      value: 0,
                      date: "",
                      dateReturn: "",
                      commentaire: ""
                    }
                    setEditingRecord(emptyRecord)
                    setIsAddDialogOpen(true)
                  }}
                  className={`bg-[#0A66C2] hover:bg-[#0A66C2]/90 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  <Plus className={`h-4 w-4`} />
                </Button>
              </div>
            </div>

            {/* Dual Search Inputs */}
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Search by Name */}
                <div className="relative">
                  <div
                    className={`flex items-center ${isRTL ? "flex-row-reverse" : ""} bg-gray-50 rounded-lg px-4 py-3 border`}
                  >
                    <Search className={`h-5 w-5 text-gray-400 ${isRTL ? "ml-3" : "mr-3"}`} />
                    <Input
                      type="text"
                      placeholder="Rechercher par nom..."
                      value={searchByName}
                      onChange={(e) => setSearchByName(e.target.value)}
                      className={`border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 p-0 ${isRTL ? "text-right" : "text-left"}`}
                      dir={isRTL ? "rtl" : "ltr"}
                    />
                  </div>
                </div>

                {/* Search by Montant */}
                <div className="relative">
                  <div
                    className={`flex items-center ${isRTL ? "flex-row-reverse" : ""} bg-gray-50 rounded-lg px-4 py-3 border`}
                  >
                    <Search className={`h-5 w-5 text-gray-400 ${isRTL ? "ml-3" : "mr-3"}`} />
                    <Input
                      type="number"
                      placeholder="Rechercher par montant..."
                      value={searchByMontant}
                      onChange={(e) => setSearchByMontant(e.target.value)}
                      className={`border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 p-0 ${isRTL ? "text-right" : "text-left"}`}
                    />
                  </div>
                </div>
              </div>

              {/* Search Status and Clear Action */}
              <div className={`flex items-center gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                {isFiltering && (
                  <div className="flex items-center text-sm text-black bg-gray-100 px-3 py-2 rounded-lg">
                    <Search className="h-4 w-4 ml-2" />
                    {filteredRecords.length} personnes sur {transformedRecords.length} affichées
                  </div>
                )}

                {(searchByName.trim() || searchByMontant.trim()) && (
                  <Button
                    variant="outline"
                    onClick={clearSearch}
                    className="border-gray-300"
                    dir="rtl"
                  >
                    <X className="h-4 w-4 ml-2" />
                    Supprimer
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>

          <CardContent className="p-6">
            {recordsError && (
              <div className="text-center py-8">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                  <p className="text-red-600 font-medium">Error loading records</p>
                  <p className="text-red-500 text-sm mt-1">{(recordsError as any).message}</p>
                  <button
                    onClick={() => refetchRecords()}
                    className="mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                  >
                    Retry
                  </button>
                </div>
              </div>
            )}

            {isLoadingRecords ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-500">Loading records...</p>
              </div>
            ) : filteredRecords.length === 0 && !recordsError ? (
              <div className="text-center py-8">
                <Search className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-gray-500">
                  {(searchByName.trim() || searchByMontant.trim())
                    ? "No records found matching your search criteria"
                    : "No records available"}
                </p>
                {(searchByName.trim() || searchByMontant.trim()) && (
                  <Button variant="outline" onClick={clearSearch} className="mt-2" dir="rtl">
                    مسح البحث
                  </Button>
                )}
              </div>
            ) : !isLoadingRecords && !recordsError ? (
              <>
              {/* Mobile Card Layout - Beautiful Dark Design Like Screenshot */}
              <div className="block md:hidden space-y-4">
                {recordsToDisplay.map((record) => {
                  const isContentHidden = hiddenContent.has(record.id)

                  return (
                    <div
                      key={record.id}
                      className="bg-white rounded-xl p-4 shadow-lg cursor-pointer hover:shadow-xl transition-all duration-300 border border-gray-200"
                      onClick={() => handleRecordClick(record)}
                    >
                      {/* Name and Amount in Same Row */}
                      <div className="flex items-center justify-between mb-4">
                        <div className="text-xl font-bold text-black">
                          {isPriceHidden ? "••••••" : formatCurrencyFrench(record.value)}
                        </div>
                        <h3 className="text-lg font-medium text-gray-900">
                          {isContentHidden
                            ? "********"
                            : (record.nameKey.startsWith("record.") ? t(record.nameKey) : record.nameKey)
                          }
                        </h3>
                      </div>

                      {/* Bottom Row: Dates Section */}
                      <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                        <div>
                          <div className="text-gray-600 mb-1">تاريخ الخروج</div>
                          <div className="flex items-center space-x-1">
                            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                            <span className="font-medium text-gray-800">
                              {record.date ? formatDate(record.date) : "-"}
                            </span>
                          </div>
                        </div>
                        <div>
                          <div className="text-gray-600 mb-1">تاريخ التحصيل</div>
                          <div className="flex items-center space-x-1">
                            <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                            <span className="font-medium text-gray-800">
                              {record.dateReturn ? formatDate(record.dateReturn) : "-"}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Note/Comment Section */}
                      <div className="border-t border-gray-100 pt-3">
                        <div className="flex items-start space-x-2 space-x-reverse" dir="rtl">
                          <div className="flex-shrink-0 mt-0.5">
                            <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="text-gray-600 text-xs mb-1" dir="rtl">ملاحظة</div>
                            <p className={`text-sm leading-relaxed ${
                              record.commentaire && record.commentaire.trim() && record.commentaire !== "لا يوجد تعليق"
                                ? "text-red-500 font-extrabold"
                                : "text-gray-800"
                            }`} dir="rtl">
                              {record.commentaire || "لا يوجد تعليق"}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>

              {/* Desktop Table Layout */}
              <div className="hidden md:block overflow-x-auto" dir="rtl">
                <table className="w-full border-collapse" dir="rtl">
                  <thead>
                    <tr className="border-b border-gray-200 bg-gray-50">
                      {/* RTL order from right: NAME first, then MONTANT, DATE sortie, DATE retour, NOTE */}
                      <th className="p-3 text-right font-semibold text-gray-900" dir="ltr">
                        {t("table.name")}
                      </th>
                      <th className="p-3 text-right font-semibold text-gray-900" dir="ltr">
                        {t("table.amount")}
                      </th>
                      <th className="p-3 text-right font-semibold text-gray-900" dir="ltr">
                        {t("table.date.out")}
                      </th>
                      <th className="p-3 text-right font-semibold text-gray-900" dir="ltr">
                        تاريخ التحصيل
                      </th>
                      <th className="p-3 text-right font-semibold text-gray-900" dir="ltr">
                        ملاحظة
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {recordsToDisplay.map((record, index) => {
                      const isContentHidden = hiddenContent.has(record.id)

                      return (
                        <tr
                          key={record.id}
                          className={`border-b border-gray-100 hover:bg-gray-50 transition-colors cursor-pointer ${
                            index % 2 === 0 ? "bg-white" : "bg-gray-25"
                          }`}
                          onClick={() => handleRecordClick(record)}
                        >
                          {/* Name column - rightmost, simple click to hide */}
                          <td className="p-3 text-right" dir="ltr">
                            <span
                              className="font-semibold text-gray-900 cursor-pointer select-none hover:scale-105 transition-transform duration-200"
                              onClick={(e) => {
                                e.preventDefault()
                                e.stopPropagation()
                                toggleContentVisibility(record.id)
                              }}
                            >
                              {isContentHidden
                                ? "********"
                                : (record.nameKey.startsWith("record.") ? t(record.nameKey) : record.nameKey)
                              }
                            </span>
                          </td>

                          {/* Amount column */}
                          <td className="p-3 text-right" dir="ltr">
                            <span className="font-medium">
                              {formatCurrencyFrench(record.value)}
                            </span>
                          </td>

                          {/* Date Out column */}
                          <td className="p-3 text-right" dir="ltr">
                            <div className="text-sm">
                              {formatDate(record.date)}
                            </div>
                          </td>

                          {/* Date Return column */}
                          <td className="p-3 text-right" dir="ltr">
                            <div className="text-sm">
                              {record.dateReturn ? formatDate(record.dateReturn) : "-"}
                            </div>
                          </td>

                          {/* Note column */}
                          <td className="p-3 text-right max-w-xs" dir="ltr">
                            <div className="flex items-center space-x-2 space-x-reverse">
                              <svg className="w-3 h-3 text-blue-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                              </svg>
                              <span className={`text-sm truncate ${
                                record.commentaire && record.commentaire.trim() && record.commentaire !== "لا يوجد تعليق"
                                  ? "text-red-500 font-extrabold"
                                  : "text-gray-600"
                              }`}>
                                {record.commentaire || "لا يوجد تعليق"}
                              </span>
                            </div>
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>
              </>
            ) : null}
          </CardContent>
        </Card>

        {/* Edit Record Dialog */}
        <EditRecordDialog
          record={editingRecord}
          isOpen={isEditDialogOpen}
          mode="edit"
          onClose={() => {
            setIsEditDialogOpen(false)
            setEditingRecord(null)
          }}
          onSave={async (updatedRecordData) => {
            if (editingRecord && updatedRecordData) {
              // Transform the data to match API expectations
              const apiData = {
                nomComplet: updatedRecordData.nameKey,
                montant: updatedRecordData.value,
                dateSortie: updatedRecordData.date,
                dateRetour: updatedRecordData.dateReturn || "",
                commentaire: updatedRecordData.commentaire || null
              }
              await handleUpdateRecord(editingRecord.id, apiData)
              setIsEditDialogOpen(false)
              setEditingRecord(null)
            }
          }}
        />

        {/* Add Record Dialog */}
        <EditRecordDialog
          record={editingRecord}
          isOpen={isAddDialogOpen}
          mode="add"
          onClose={() => {
            setIsAddDialogOpen(false)
            setEditingRecord(null)
          }}
          onSave={async (recordData) => {
            console.log("💾 Add dialog onSave called with:", recordData)
            // Transform the data to match API expectations
            const apiData = {
              nomComplet: recordData.nameKey,
              montant: recordData.value,
              dateSortie: recordData.date,
              dateRetour: recordData.dateReturn || "",
              commentaire: recordData.commentaire || null
            }
            console.log("🔄 Transformed data for API:", apiData)
            await handleCreateRecord(apiData)
            setIsAddDialogOpen(false)
            setEditingRecord(null)
          }}
        />

        {/* Mobile Action Menu Dialog */}
        {isActionMenuOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-end justify-center z-50"
            onClick={() => setIsActionMenuOpen(false)}
          >
            <div
              className="bg-white rounded-t-lg w-full max-w-md p-6 space-y-4"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900">
                  {selectedRecord?.nameKey.startsWith("record.") ? t(selectedRecord.nameKey) : selectedRecord?.nameKey}
                </h3>
                <p className="text-sm text-gray-500 mt-1">
                  {t("actions.choose")}
                </p>
              </div>

              <div className="space-y-3">
                <Button
                  onClick={handleActionMenuEdit}
                  className="w-full flex items-center justify-center space-x-2 bg-gray-800 hover:bg-black"
                >
                  <Edit className="h-4 w-4" />
                  <span>{t("actions.edit")}</span>
                </Button>

                <Button
                  onClick={() => {
                    setIsActionMenuOpen(false)
                    if (selectedRecord) {
                      handleDeleteRecord(selectedRecord.id)
                    }
                  }}
                  variant="destructive"
                  className="w-full flex items-center justify-center space-x-2"
                >
                  <Trash2 className="h-4 w-4" />
                  <span>{t("actions.delete")}</span>
                </Button>

                <Button
                  onClick={() => setIsActionMenuOpen(false)}
                  variant="outline"
                  className="w-full"
                >
                  {t("actions.cancel")}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Table Edit Modal */}
        <Dialog open={isTableEditOpen} onOpenChange={setIsTableEditOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="text-right" dir="rtl">تعديل الجدول</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="tableName" className="text-right block mb-2" dir="rtl">
                  اسم الجدول
                </Label>
                <Input
                  id="tableName"
                  value={editTableName}
                  onChange={(e) => setEditTableName(e.target.value)}
                  placeholder="أدخل اسم الجدول"
                  className="text-right"
                  dir="rtl"
                />
              </div>

              <div>
                <Label htmlFor="tableInitialAmount" className="text-right block mb-2" dir="rtl">
                  المبلغ الأولي
                </Label>
                <Input
                  id="tableInitialAmount"
                  type="text"
                  value={displayInitialAmount}
                  onChange={(e) => {
                    const inputValue = e.target.value

                    // Allow only digits, remove any other characters (including existing dots)
                    const cleanInput = inputValue.replace(/[^\d]/g, '')

                    // If empty, set empty display and empty form value
                    if (cleanInput === "") {
                      setDisplayInitialAmount("")
                      setEditTableInitialAmount("0")
                      return
                    }

                    // Parse the number
                    const numValue = parseInt(cleanInput) || 0

                    // Format and display the number with thousands separators
                    if (numValue > 0) {
                      const formatted = formatNumberDisplay(numValue)
                      setDisplayInitialAmount(formatted)
                      setEditTableInitialAmount(numValue.toString())
                    } else {
                      setDisplayInitialAmount("")
                      setEditTableInitialAmount("0")
                    }
                  }}
                  placeholder="أدخل المبلغ الأولي"
                  className="text-right"
                  dir="rtl"
                  step="0.01"
                  min="0"
                />
              </div>

              <div>
                <Label className="text-right block mb-2" dir="rtl">
                  لون الجدول
                </Label>
                <div className="flex items-center space-x-3 space-x-reverse" dir="rtl">

                  {/* Color Picker Input */}
                  <div className="flex-1">
                    <input
                      type="color"
                      value={editTableColor}
                      onChange={(e) => setEditTableColor(e.target.value)}
                      className="w-full h-12 rounded-lg border-2 border-gray-300 cursor-pointer"
                      title="اختر لون الجدول"
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setIsTableEditOpen(false)}
                  disabled={updateTableMutation.isPending}
                >
                  إلغاء
                </Button>
                <Button
                  onClick={handleSaveTableChanges}
                  disabled={updateTableMutation.isPending}
                  className="bg-[#0A66C2] hover:bg-[#004182] text-white"
                >
                  {updateTableMutation.isPending ? "جاري الحفظ..." : "حفظ"}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </main>

      {/* Fixed Floating Action Buttons - Stacked Layout */}
      <div className="fixed bottom-6 right-6 z-50 flex flex-col space-y-4">
        {/* PDF Download Button - Always visible */}
        <Button
          onClick={handlePDFDownload}
          disabled={isGenerating || !recordsResponse}
          className={`p-5 w-16 h-16 ${isGenerating ? 'bg-gray-400' : 'bg-red-500 hover:bg-red-600'} text-white rounded-full shadow-2xl transition-all duration-300 hover:scale-110 border-2 border-white`}
          title="تحميل PDF"
        >
          {isGenerating ? (
            <RefreshCw className="h-12 w-12 animate-spin" />
          ) : (
            <Download className="h-12 w-12" />
          )}
        </Button>

        {/* Scroll to Top Button - Only when sticky summary is shown */}
        {showStickySummary && (
          <Button
            onClick={scrollToTop}
            className="p-5 w-16 h-16 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-2xl transition-all duration-300 hover:scale-110 border-2 border-white"
            title="العودة إلى الأعلى"
          >
            <ChevronUp className="h-12 w-12" />
          </Button>
        )}
      </div>
    </div>
  )
}
